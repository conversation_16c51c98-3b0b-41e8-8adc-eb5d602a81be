import { isFunction } from '../function'
import { type CreateDeferredOptions, type DeferredPromise, createDeferred } from './deferred'

export type ErrorOrFactory = string | Error | (() => string | Error)

export function createDeferredWithTimeout<T>(
    timeout: number,
    errorOrFactory: ErrorOrFactory,
    options?: CreateDeferredOptions<T>,
): DeferredPromise<T> {
    const deferred = createDeferred<T>(options)
    let timeoutId: NodeJS.Timeout | undefined

    const createTimeoutError = (): Error => {
        if (isFunction(errorOrFactory)) {
            const result = errorOrFactory()

            return result instanceof Error ? result : new Error(result)
        }

        if (errorOrFactory instanceof Error) {
            return errorOrFactory
        }

        return new Error(errorOrFactory)
    }

    const clearTimer = () => {
        if (timeoutId !== undefined) {
            clearTimeout(timeoutId)
            timeoutId = undefined
        }
    }

    const originalResolve = deferred.resolve
    const originalReject = deferred.reject

    deferred.resolve = (value: T | PromiseLike<T>) => {
        clearTimer()
        originalResolve(value)
    }

    deferred.reject = (reason?: unknown) => {
        clearTimer()
        originalReject(reason)
    }

    if (timeout > 0) {
        timeoutId = setTimeout(() => {
            if (!deferred.isSettled) {
                deferred.reject(createTimeoutError())
            }
        }, timeout)
    }

    return deferred
}
